nohup: ignoring input
 * Serving Flask app 'main'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug: * Restarting with stat
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 120-485-730
INFO:werkzeug:127.0.0.1 - - [09/Jul/2025 03:05:20] "POST /api/notes/import HTTP/1.1" 200 -
