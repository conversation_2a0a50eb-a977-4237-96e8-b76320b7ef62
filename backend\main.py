import os

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.models.note import db
from src.routes.notes import notes_bp
from src.routes.user import user_bp

# Get the project root directory (parent of backend)
project_root = os.path.dirname(os.path.dirname(__file__))

# Static folder points to frontend dist directory for production
# For development, this will serve the frontend build
static_folder = os.path.join(project_root, 'frontend', 'dist')
if not os.path.exists(static_folder):
    # Fallback to frontend public folder for development
    static_folder = os.path.join(project_root, 'frontend', 'public')

app = Flask(__name__, static_folder=static_folder)
app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'

# Enable CORS for all routes
CORS(app)

app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(notes_bp, url_prefix='/api')

# Database configuration - points to data directory
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(project_root, 'data', 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
