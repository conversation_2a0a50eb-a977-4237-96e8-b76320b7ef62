import os

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.config import get_config
from src.models import db
from src.routes.notes import notes_bp
from src.utils.logging_config import setup_logging

# Get the project root directory (parent of backend)
project_root = os.path.dirname(os.path.dirname(__file__))

# Load configuration
config = get_config()

# Ensure required directories exist
config.ensure_directories(project_root)

# Initialize logging
logger = setup_logging(config.LOG_LEVEL, config.LOG_FILE)

# Static folder points to frontend dist directory for production
# For development, this will serve the frontend build
static_folder = os.path.join(project_root, 'frontend', 'dist')
if not os.path.exists(static_folder):
    # Fallback to frontend public folder for development
    static_folder = os.path.join(project_root, 'frontend', 'public')

app = Flask(__name__, static_folder=static_folder)

# Apply configuration
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['DEBUG'] = config.DEBUG
app.config['SQLALCHEMY_DATABASE_URI'] = config.get_database_uri(project_root)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
app.config['SQLALCHEMY_ECHO'] = config.SQLALCHEMY_ECHO

# Enable CORS for all routes
CORS(app, origins=config.CORS_ORIGINS)

app.register_blueprint(notes_bp, url_prefix='/api')
db.init_app(app)
with app.app_context():
    db.create_all()

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG)
