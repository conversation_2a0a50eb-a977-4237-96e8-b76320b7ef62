# Note Manager - Local Knowledge Management System

A full-stack application for managing notes and LLM conversations locally with graph visualization and intelligent content processing.

## 📁 Project Structure

```
project-root/
├── backend/                    # Flask backend application
│   ├── src/
│   │   ├── models/            # SQLAlchemy models
│   │   │   ├── __init__.py
│   │   │   ├── note.py        # Note, Tag, NoteLink, ArchivedFile models
│   │   │   └── user.py        # User model
│   │   ├── routes/            # Flask blueprints/routes
│   │   │   ├── __init__.py
│   │   │   ├── notes.py       # Note management endpoints
│   │   │   └── user.py        # User management endpoints
│   │   ├── services/          # Business logic services
│   │   │   ├── __init__.py
│   │   │   ├── file_service.py # File import and processing
│   │   │   └── llm_service.py  # LLM integration
│   │   └── database/          # Database files (auto-created)
│   ├── tests/                 # Backend tests
│   ├── main.py               # Flask application entry point
│   ├── requirements.txt      # Python dependencies
│   └── flask.log            # Application logs
├── frontend/                  # React frontend application
│   ├── src/
│   │   ├── components/       # React components
│   │   │   ├── ui/          # Reusable UI components
│   │   │   ├── App.jsx      # Main application component
│   │   │   ├── GraphView.jsx # Graph visualization
│   │   │   ├── ImportDialog.jsx # File import dialog
│   │   │   ├── NoteList.jsx  # Note listing component
│   │   │   └── NoteViewer.jsx # Note viewing/editing
│   │   ├── main.jsx         # React entry point
│   │   └── App.css          # Application styles
│   ├── public/              # Static assets
│   ├── index.html           # HTML template
│   ├── vite.config.js       # Vite configuration
│   └── package.json         # Node.js dependencies
├── data/                     # Application data
│   ├── notes/               # Processed note files
│   ├── archive/             # Original file archives
│   └── database/            # SQLite database files
├── docs/                    # Documentation
│   ├── Note Manager - Local Knowledge Management System.md
│   ├── Note Manager_ Local Knowledge Management System.md
│   ├── Research Summary_ Note Management Tool with LLM Integration.md
│   └── System Architecture Design for Local Note Management Tool.md
└── README.md               # This file
```

## 🚀 Quick Start

### Backend Setup

1. **Navigate to backend directory:**

   ```bash
   cd backend
   ```

2. **Create and activate virtual environment:**

   ```bash
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

4. **Run the backend:**

   ```bash
   python main.py
   ```

   The backend will start on `http://localhost:5000`

### Frontend Setup

1. **Navigate to frontend directory:**

   ```bash
   cd frontend
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Run the development server:**

   ```bash
   npm run dev
   ```

   The frontend will start on `http://localhost:5173`

## 📖 Features

- **Note Management**: Import and organize markdown and JSON files
- **LLM Integration**: Automatic topic extraction, tagging, and summarization
- **Graph Visualization**: Interactive network view of note relationships
- **Search & Filter**: Full-text search and tag-based filtering
- **File Archive**: Automatic backup of original files
- **Local Storage**: All data stored locally with SQLite database

## 🛠️ Development

### Backend Development

- Flask application with SQLAlchemy ORM
- RESTful API endpoints
- LiteLLM integration for multiple LLM providers
- Automatic database migrations

### Frontend Development

- React 18 with Vite
- Component-based architecture
- Responsive design
- Real-time updates

## 📝 API Endpoints

- `GET /api/notes` - List all notes
- `POST /api/notes/import` - Import files/directories
- `GET /api/notes/search` - Search notes
- `GET /api/notes/graph` - Get graph data
- `PUT /api/notes/<id>` - Update note content

## 🔧 Configuration

### LLM Configuration

Set environment variables for LLM providers:

```bash
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
```

### Database Configuration

Database is automatically created in `data/database/app.db`

### File Storage

- Notes: `data/notes/`
- Archive: `data/archive/`
- Database: `data/database/`

## 📚 Documentation

See the `docs/` directory for detailed documentation including:

- System architecture design
- API documentation
- Development guides
- Research summaries
