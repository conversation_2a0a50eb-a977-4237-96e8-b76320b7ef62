"""
Configuration management for AIrchivist application
"""
import os
from typing import Optional


class Config:
    """Base configuration class"""
    
    # Database configuration
    DATABASE_URI = os.getenv(
        'DATABASE_URI', 
        'sqlite:///data/database/app.db'
    )
    
    # Security
    SECRET_KEY = os.getenv(
        'SECRET_KEY', 
        'dev-secret-key-change-in-production'
    )
    
    # Flask configuration
    DEBUG = os.getenv('DEBUG', 'False').lower() in ('true', '1', 'yes', 'on')
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', '5000'))
    
    # CORS configuration
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', '*').split(',')
    
    # File storage configuration
    NOTES_DIRECTORY = os.getenv('NOTES_DIRECTORY', 'data/notes')
    ARCHIVE_DIRECTORY = os.getenv('ARCHIVE_DIRECTORY', 'data/archive')
    DATABASE_DIRECTORY = os.getenv('DATABASE_DIRECTORY', 'data/database')
    LOGS_DIRECTORY = os.getenv('LOGS_DIRECTORY', 'data/logs')
    
    # LLM configuration
    LLM_MODEL_NAME = os.getenv('LLM_MODEL_NAME', 'ollama/llama3.2')
    LLM_API_BASE = os.getenv('LLM_API_BASE', 'http://localhost:11434')
    LLM_TEMPERATURE = float(os.getenv('LLM_TEMPERATURE', '0.3'))
    LLM_MAX_TOKENS = int(os.getenv('LLM_MAX_TOKENS', '2000'))
    
    # API Keys for external LLM providers
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE')  # If None, will use default location
    
    # Pagination defaults
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '20'))
    MAX_PAGE_SIZE = int(os.getenv('MAX_PAGE_SIZE', '100'))
    
    # File processing limits
    MAX_FILE_SIZE_MB = int(os.getenv('MAX_FILE_SIZE_MB', '10'))
    SUPPORTED_FILE_TYPES = os.getenv('SUPPORTED_FILE_TYPES', '.md,.json').split(',')
    
    # SQLAlchemy configuration
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = os.getenv('SQLALCHEMY_ECHO', 'False').lower() in ('true', '1', 'yes', 'on')
    
    @classmethod
    def get_database_uri(cls, project_root: str) -> str:
        """
        Get the complete database URI with absolute path
        
        Args:
            project_root: Project root directory
            
        Returns:
            Complete database URI
        """
        if cls.DATABASE_URI.startswith('sqlite:///'):
            # Handle relative SQLite paths
            db_path = cls.DATABASE_URI[10:]  # Remove 'sqlite:///'
            if not os.path.isabs(db_path):
                db_path = os.path.join(project_root, db_path)
            return f'sqlite:///{db_path}'
        return cls.DATABASE_URI
    
    @classmethod
    def get_absolute_path(cls, relative_path: str, project_root: str) -> str:
        """
        Convert relative path to absolute path
        
        Args:
            relative_path: Relative path from project root
            project_root: Project root directory
            
        Returns:
            Absolute path
        """
        if os.path.isabs(relative_path):
            return relative_path
        return os.path.join(project_root, relative_path)
    
    @classmethod
    def ensure_directories(cls, project_root: str):
        """
        Ensure all required directories exist
        
        Args:
            project_root: Project root directory
        """
        directories = [
            cls.get_absolute_path(cls.NOTES_DIRECTORY, project_root),
            cls.get_absolute_path(cls.ARCHIVE_DIRECTORY, project_root),
            cls.get_absolute_path(cls.DATABASE_DIRECTORY, project_root),
            cls.get_absolute_path(cls.LOGS_DIRECTORY, project_root),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'INFO'
    SECRET_KEY = os.getenv('SECRET_KEY')  # Must be set in production
    
    @classmethod
    def validate(cls):
        """Validate production configuration"""
        if not cls.SECRET_KEY:
            raise ValueError("SECRET_KEY must be set in production")


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URI = 'sqlite:///:memory:'
    LOG_LEVEL = 'WARNING'


# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config(config_name: Optional[str] = None) -> Config:
    """
    Get configuration class based on environment
    
    Args:
        config_name: Configuration name (development, production, testing)
        
    Returns:
        Configuration class instance
    """
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    return config_map.get(config_name, DevelopmentConfig)
