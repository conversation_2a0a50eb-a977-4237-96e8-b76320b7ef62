import litellm
import json
import re
from typing import List, Dict, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMService:
    def __init__(self, model_name: str = "ollama/llama3.2", api_base: str = "http://localhost:11434"):
        """
        Initialize LLM service with LiteLLM
        
        Args:
            model_name: The model to use (default: local Ollama Llama3.2)
            api_base: Base URL for the LLM API
        """
        self.model_name = model_name
        self.api_base = api_base
        
        # Configure LiteLLM for local Ollama
        litellm.set_verbose = False
        
    def extract_topics(self, content: str, max_topics: int = 5) -> List[str]:
        """
        Extract key topics from note content using LLM
        
        Args:
            content: The text content to analyze
            max_topics: Maximum number of topics to extract
            
        Returns:
            List of extracted topics
        """
        try:
            prompt = f"""
            Analyze the following text and extract the {max_topics} most important topics or themes.
            Return only a JSON array of topic strings, no additional text.
            
            Text:
            {content[:2000]}  # Limit content to avoid token limits
            
            Example response: ["machine learning", "data science", "neural networks"]
            """
            
            response = litellm.completion(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                api_base=self.api_base,
                temperature=0.3
            )
            
            result = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                topics = json.loads(result)
                if isinstance(topics, list):
                    return [str(topic).lower().strip() for topic in topics[:max_topics]]
            except json.JSONDecodeError:
                # Fallback: extract topics from text response
                topics = self._extract_topics_fallback(result)
                return topics[:max_topics]
                
        except Exception as e:
            logger.error(f"Error extracting topics: {e}")
            return self._extract_topics_fallback(content)
    
    def generate_tags(self, content: str, existing_tags: List[str] = None, max_tags: int = 8) -> List[str]:
        """
        Generate relevant tags for note content
        
        Args:
            content: The text content to analyze
            existing_tags: List of existing tags to consider
            max_tags: Maximum number of tags to generate
            
        Returns:
            List of generated tags
        """
        try:
            existing_tags_str = ", ".join(existing_tags) if existing_tags else "none"
            
            prompt = f"""
            Generate relevant tags for the following text content. 
            Consider existing tags: {existing_tags_str}
            
            Return only a JSON array of tag strings (single words or short phrases), no additional text.
            Focus on: categories, technologies, concepts, subjects, and key themes.
            
            Text:
            {content[:2000]}
            
            Example response: ["python", "web-development", "tutorial", "backend"]
            """
            
            response = litellm.completion(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                api_base=self.api_base,
                temperature=0.4
            )
            
            result = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                tags = json.loads(result)
                if isinstance(tags, list):
                    return [str(tag).lower().strip().replace(" ", "-") for tag in tags[:max_tags]]
            except json.JSONDecodeError:
                # Fallback: extract tags from text response
                tags = self._extract_tags_fallback(result)
                return tags[:max_tags]
                
        except Exception as e:
            logger.error(f"Error generating tags: {e}")
            return self._extract_tags_fallback(content)
    
    def summarize_content(self, content: str, max_length: int = 200) -> str:
        """
        Generate a summary of the note content
        
        Args:
            content: The text content to summarize
            max_length: Maximum length of summary in words
            
        Returns:
            Generated summary
        """
        try:
            prompt = f"""
            Create a concise summary of the following text in approximately {max_length} words.
            Focus on the main points, key insights, and important information.
            Return only the summary text, no additional formatting.
            
            Text:
            {content[:3000]}
            """
            
            response = litellm.completion(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                api_base=self.api_base,
                temperature=0.3
            )
            
            summary = response.choices[0].message.content.strip()
            
            # Ensure summary is not too long
            words = summary.split()
            if len(words) > max_length:
                summary = " ".join(words[:max_length]) + "..."
                
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return self._generate_fallback_summary(content, max_length)
    
    def _extract_topics_fallback(self, text: str) -> List[str]:
        """
        Fallback method to extract topics using simple text processing
        """
        # Simple keyword extraction as fallback
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        # Remove common words
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'many', 'oil', 'sit', 'set', 'run', 'eat', 'far', 'sea', 'eye', 'ask', 'own', 'say', 'too', 'any', 'try', 'let', 'put', 'end', 'why', 'turn', 'here', 'show', 'every', 'good', 'me', 'give', 'our', 'under', 'name', 'very', 'through', 'just', 'form', 'sentence', 'great', 'think', 'help', 'low', 'line', 'differ', 'turn', 'cause', 'much', 'mean', 'before', 'move', 'right', 'boy', 'old', 'too', 'same', 'tell', 'does', 'set', 'three', 'want', 'air', 'well', 'also', 'play', 'small', 'end', 'put', 'home', 'read', 'hand', 'port', 'large', 'spell', 'add', 'even', 'land', 'here', 'must', 'big', 'high', 'such', 'follow', 'act', 'why', 'ask', 'men', 'change', 'went', 'light', 'kind', 'off', 'need', 'house', 'picture', 'try', 'us', 'again', 'animal', 'point', 'mother', 'world', 'near', 'build', 'self', 'earth', 'father', 'head', 'stand', 'own', 'page', 'should', 'country', 'found', 'answer', 'school', 'grow', 'study', 'still', 'learn', 'plant', 'cover', 'food', 'sun', 'four', 'between', 'state', 'keep', 'eye', 'never', 'last', 'let', 'thought', 'city', 'tree', 'cross', 'farm', 'hard', 'start', 'might', 'story', 'saw', 'far', 'sea', 'draw', 'left', 'late', 'run', 'don', 'while', 'press', 'close', 'night', 'real', 'life', 'few', 'north', 'open', 'seem', 'together', 'next', 'white', 'children', 'begin', 'got', 'walk', 'example', 'ease', 'paper', 'group', 'always', 'music', 'those', 'both', 'mark', 'often', 'letter', 'until', 'mile', 'river', 'car', 'feet', 'care', 'second', 'book', 'carry', 'took', 'science', 'eat', 'room', 'friend', 'began', 'idea', 'fish', 'mountain', 'stop', 'once', 'base', 'hear', 'horse', 'cut', 'sure', 'watch', 'color', 'face', 'wood', 'main', 'enough', 'plain', 'girl', 'usual', 'young', 'ready', 'above', 'ever', 'red', 'list', 'though', 'feel', 'talk', 'bird', 'soon', 'body', 'dog', 'family', 'direct', 'leave', 'song', 'measure', 'door', 'product', 'black', 'short', 'numeral', 'class', 'wind', 'question', 'happen', 'complete', 'ship', 'area', 'half', 'rock', 'order', 'fire', 'south', 'problem', 'piece', 'told', 'knew', 'pass', 'since', 'top', 'whole', 'king', 'space', 'heard', 'best', 'hour', 'better', 'during', 'hundred', 'five', 'remember', 'step', 'early', 'hold', 'west', 'ground', 'interest', 'reach', 'fast', 'verb', 'sing', 'listen', 'six', 'table', 'travel', 'less', 'morning', 'ten', 'simple', 'several', 'vowel', 'toward', 'war', 'lay', 'against', 'pattern', 'slow', 'center', 'love', 'person', 'money', 'serve', 'appear', 'road', 'map', 'rain', 'rule', 'govern', 'pull', 'cold', 'notice', 'voice', 'unit', 'power', 'town', 'fine', 'certain', 'fly', 'fall', 'lead', 'cry', 'dark', 'machine', 'note', 'wait', 'plan', 'figure', 'star', 'box', 'noun', 'field', 'rest', 'correct', 'able', 'pound', 'done', 'beauty', 'drive', 'stood', 'contain', 'front', 'teach', 'week', 'final', 'gave', 'green', 'oh', 'quick', 'develop', 'ocean', 'warm', 'free', 'minute', 'strong', 'special', 'mind', 'behind', 'clear', 'tail', 'produce', 'fact', 'street', 'inch', 'multiply', 'nothing', 'course', 'stay', 'wheel', 'full', 'force', 'blue', 'object', 'decide', 'surface', 'deep', 'moon', 'island', 'foot', 'system', 'busy', 'test', 'record', 'boat', 'common', 'gold', 'possible', 'plane', 'stead', 'dry', 'wonder', 'laugh', 'thousands', 'ago', 'ran', 'check', 'game', 'shape', 'equate', 'hot', 'miss', 'brought', 'heat', 'snow', 'tire', 'bring', 'yes', 'distant', 'fill', 'east', 'paint', 'language', 'among'}
        
        filtered_words = [word for word in words if word not in common_words and len(word) > 3]
        
        # Get most frequent words as topics
        from collections import Counter
        word_counts = Counter(filtered_words)
        return [word for word, count in word_counts.most_common(5)]
    
    def _extract_tags_fallback(self, text: str) -> List[str]:
        """
        Fallback method to extract tags using simple text processing
        """
        # Extract potential tags from text
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        # Filter and format as tags
        tags = []
        for word in words[:10]:
            if len(word) > 2 and word not in ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out']:
                tags.append(word.replace(" ", "-"))
        return tags[:8]
    
    def _generate_fallback_summary(self, content: str, max_length: int) -> str:
        """
        Fallback method to generate summary using simple text processing
        """
        sentences = re.split(r'[.!?]+', content)
        # Take first few sentences as summary
        summary_sentences = []
        word_count = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                words = sentence.split()
                if word_count + len(words) <= max_length:
                    summary_sentences.append(sentence)
                    word_count += len(words)
                else:
                    break
        
        summary = ". ".join(summary_sentences)
        if summary and not summary.endswith('.'):
            summary += "."
            
        return summary if summary else content[:500] + "..."
    
    def test_connection(self) -> bool:
        """
        Test if the LLM service is available
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            response = litellm.completion(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello, respond with 'OK'"}],
                api_base=self.api_base,
                max_tokens=10
            )
            return "ok" in response.choices[0].message.content.lower()
        except Exception as e:
            logger.error(f"LLM connection test failed: {e}")
            return False

